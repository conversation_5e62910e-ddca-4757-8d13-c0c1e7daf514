<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="用户ID" prop="userId">
        <el-input
          v-model="queryParams.userId"
          placeholder="请输入用户ID"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-check"
          size="mini"
          @click="handleValidateAll"
        >验证所有用户</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-edit-outline"
          size="mini"
          @click="handleFixAll"
        >修复所有用户</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <!-- 统计信息 -->
    <el-row :gutter="20" class="mb8" v-if="statistics.totalUsers > 0">
      <el-col :span="6">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>总用户数</span>
          </div>
          <div class="text item">
            <span class="number">{{ statistics.totalUsers }}</span>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>数据一致</span>
          </div>
          <div class="text item">
            <span class="number success">{{ statistics.consistentCount }}</span>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>数据不一致</span>
          </div>
          <div class="text item">
            <span class="number danger">{{ statistics.inconsistentCount }}</span>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>一致性比例</span>
          </div>
          <div class="text item">
            <span class="number">{{ consistencyRate }}%</span>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <el-table v-loading="loading" :data="userList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="用户ID" align="center" prop="userId" />
      <el-table-column label="用户昵称" align="center" prop="userName" />
      <el-table-column label="总余额" align="center" prop="totalBalance">
        <template slot-scope="scope">
          <span>¥{{ scope.row.totalBalance }}</span>
        </template>
      </el-table-column>
      <el-table-column label="可用余额" align="center" prop="availableBalance">
        <template slot-scope="scope">
          <span>¥{{ scope.row.availableBalance }}</span>
        </template>
      </el-table-column>
      <el-table-column label="累计收入" align="center" prop="totalIncome">
        <template slot-scope="scope">
          <span>¥{{ scope.row.totalIncome }}</span>
        </template>
      </el-table-column>
      <el-table-column label="累计提现" align="center" prop="totalWithdraw">
        <template slot-scope="scope">
          <span>¥{{ scope.row.totalWithdraw }}</span>
        </template>
      </el-table-column>
      <el-table-column label="数据状态" align="center">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.isConsistent" type="success">一致</el-tag>
          <el-tag v-else type="danger">不一致</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-check"
            @click="handleValidate(scope.row)"
          >验证</el-button>
          <el-button
            v-if="!scope.row.isConsistent"
            size="mini"
            type="text"
            icon="el-icon-edit-outline"
            @click="handleFix(scope.row)"
          >修复</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import { validateUserBalance, validateAllUserBalance, fixUserBalance, fixAllUserBalance } from "@/api/fuguang/balanceConsistency";

export default {
  name: "BalanceConsistency",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 用户列表
      userList: [],
      // 统计数据
      statistics: {},
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        userId: null,
      },
    };
  },
  computed: {
    consistencyRate() {
      if (this.statistics.totalUsers === 0) return 0;
      return Math.round((this.statistics.consistentCount / this.statistics.totalUsers) * 100);
    }
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询用户列表 */
    getList() {
      this.loading = true;
      if (this.queryParams.userId) {
        // 验证单个用户
        validateUserBalance(this.queryParams.userId).then(response => {
          this.userList = [response.data];
          this.total = 1;
          this.statistics = {
            totalUsers: 1,
            consistentCount: response.data.isConsistent ? 1 : 0,
            inconsistentCount: response.data.isConsistent ? 0 : 1
          };
          this.loading = false;
        });
      } else {
        // 验证所有用户
        validateAllUserBalance().then(response => {
          this.userList = response.data.details;
          this.total = response.data.totalUsers;
          this.statistics = {
            totalUsers: response.data.totalUsers,
            consistentCount: response.data.consistentCount,
            inconsistentCount: response.data.inconsistentCount
          };
          this.loading = false;
        });
      }
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.userId)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 验证单个用户 */
    handleValidate(row) {
      const userId = row.userId;
      this.$modal.loading("正在验证用户数据一致性...");
      validateUserBalance(userId).then(response => {
        this.$modal.closeLoading();
        if (response.data.isConsistent) {
          this.$modal.msgSuccess("用户数据一致性验证通过");
        } else {
          this.$modal.msgWarning("用户数据存在不一致，建议进行修复");
        }
        this.getList();
      }).catch(() => {
        this.$modal.closeLoading();
      });
    },
    /** 验证所有用户 */
    handleValidateAll() {
      this.$modal.loading("正在验证所有用户数据一致性...");
      validateAllUserBalance().then(response => {
        this.$modal.closeLoading();
        const { totalUsers, consistentCount, inconsistentCount } = response.data;
        this.$modal.msgSuccess(`验证完成：共${totalUsers}个用户，${consistentCount}个一致，${inconsistentCount}个不一致`);
        this.getList();
      }).catch(() => {
        this.$modal.closeLoading();
      });
    },
    /** 修复单个用户 */
    handleFix(row) {
      const userId = row.userId;
      const userName = row.userName;
      this.$modal.confirm('是否确认修复用户"' + userName + '"的余额数据？').then(() => {
        this.$modal.loading("正在修复用户余额数据...");
        return fixUserBalance(userId);
      }).then(() => {
        this.$modal.closeLoading();
        this.getList();
        this.$modal.msgSuccess("修复成功");
      }).catch(() => {
        this.$modal.closeLoading();
      });
    },
    /** 修复所有用户 */
    handleFixAll() {
      this.$modal.confirm('是否确认修复所有用户的余额数据？此操作可能需要较长时间。').then(() => {
        this.$modal.loading("正在批量修复用户余额数据...");
        return fixAllUserBalance();
      }).then(response => {
        this.$modal.closeLoading();
        const { fixedCount, failedCount } = response.data;
        this.getList();
        this.$modal.msgSuccess(`批量修复完成：成功修复${fixedCount}个用户，失败${failedCount}个用户`);
      }).catch(() => {
        this.$modal.closeLoading();
      });
    }
  }
};
</script>

<style scoped>
.box-card {
  margin-bottom: 20px;
}
.text {
  font-size: 14px;
}
.item {
  margin-bottom: 18px;
}
.number {
  font-size: 24px;
  font-weight: bold;
}
.success {
  color: #67C23A;
}
.danger {
  color: #F56C6C;
}
</style>
