import request from '@/utils/request'

// 验证单个用户余额数据一致性
export function validateUserBalance(userId) {
  return request({
    url: '/fuguang/balance/consistency/validate/' + userId,
    method: 'get'
  })
}

// 验证所有用户余额数据一致性
export function validateAllUserBalance() {
  return request({
    url: '/fuguang/balance/consistency/validate/all',
    method: 'get'
  })
}

// 修复单个用户余额数据
export function fixUserBalance(userId) {
  return request({
    url: '/fuguang/balance/consistency/fix/' + userId,
    method: 'post'
  })
}

// 批量修复所有用户余额数据
export function fixAllUserBalance() {
  return request({
    url: '/fuguang/balance/consistency/fix/all',
    method: 'post'
  })
}

// 获取业务类型说明
export function getBusinessTypes() {
  return request({
    url: '/fuguang/balance/consistency/business-types',
    method: 'get'
  })
}

// 获取收入类型说明
export function getIncomeTypes() {
  return request({
    url: '/fuguang/balance/consistency/income-types',
    method: 'get'
  })
}
