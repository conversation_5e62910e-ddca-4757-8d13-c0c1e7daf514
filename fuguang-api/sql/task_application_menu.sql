-- ----------------------------
-- 任务申请管理功能菜单配置SQL
-- 执行此SQL文件可为管理后台添加任务申请管理相关菜单
-- ----------------------------

-- 任务申请管理主菜单
-- 注意：parent_id需要根据实际的浮光壁垒系统菜单ID进行调整
-- 这里假设浮光壁垒系统的父菜单ID为2000，请根据实际情况修改
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES
('任务申请管理', 2000, 3, 'taskApplication', 'fuguang/taskApplication/index', '', 1, 0, 'C', '0', '0', 'fuguang:taskApplication:list', 'peoples', 'admin', sysdate(), '', null, '任务申请管理菜单');

-- 获取刚插入的任务申请管理菜单ID
SET @taskApplicationMenuId = LAST_INSERT_ID();

-- 任务申请管理功能按钮权限
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES
('任务申请查询', @taskApplicationMenuId, 1, '', '', '', 1, 0, 'F', '0', '0', 'fuguang:taskApplication:query', '#', 'admin', sysdate(), '', null, '任务申请查询权限'),
('任务申请新增', @taskApplicationMenuId, 2, '', '', '', 1, 0, 'F', '0', '0', 'fuguang:taskApplication:add', '#', 'admin', sysdate(), '', null, '任务申请新增权限'),
('任务申请修改', @taskApplicationMenuId, 3, '', '', '', 1, 0, 'F', '0', '0', 'fuguang:taskApplication:edit', '#', 'admin', sysdate(), '', null, '任务申请修改权限'),
('任务申请删除', @taskApplicationMenuId, 4, '', '', '', 1, 0, 'F', '0', '0', 'fuguang:taskApplication:remove', '#', 'admin', sysdate(), '', null, '任务申请删除权限'),
('任务申请导出', @taskApplicationMenuId, 5, '', '', '', 1, 0, 'F', '0', '0', 'fuguang:taskApplication:export', '#', 'admin', sysdate(), '', null, '任务申请导出权限'),
('确认申请', @taskApplicationMenuId, 6, '', '', '', 1, 0, 'F', '0', '0', 'fuguang:taskApplication:confirm', '#', 'admin', sysdate(), '', null, '管理员确认申请权限'),
('拒绝申请', @taskApplicationMenuId, 7, '', '', '', 1, 0, 'F', '0', '0', 'fuguang:taskApplication:reject', '#', 'admin', sysdate(), '', null, '管理员拒绝申请权限');

-- ----------------------------
-- 使用说明：
-- 1. 执行前请确认parent_id（父菜单ID）是否正确
-- 2. 可以通过以下SQL查询现有的浮光壁垒系统菜单：
--    SELECT menu_id, menu_name, parent_id FROM sys_menu WHERE menu_name LIKE '%浮光%' OR menu_name LIKE '%任务%';
-- 3. 执行后需要重新登录管理后台才能看到新菜单
-- 4. 如需删除菜单，可执行：
--    DELETE FROM sys_menu WHERE perms LIKE 'fuguang:taskApplication:%';
-- ----------------------------
