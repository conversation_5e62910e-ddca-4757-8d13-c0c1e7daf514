-- ----------------------------
-- 活动管理功能SQL脚本
-- 执行此SQL文件可为系统添加活动管理相关表和菜单
-- ----------------------------

-- ----------------------------
-- Table structure for app_activity
-- ----------------------------
DROP TABLE IF EXISTS `app_activity`;
CREATE TABLE `app_activity` (
  `activity_id` bigint NOT NULL AUTO_INCREMENT COMMENT '活动ID',
  `activity_name` varchar(100) COLLATE utf8mb4_general_ci NOT NULL COMMENT '活动名称',
  `activity_image` varchar(500) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '活动图片',
  `activity_url` varchar(500) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '活动链接',
  `activity_desc` text COLLATE utf8mb4_general_ci COMMENT '活动描述',
  `start_time` datetime DEFAULT NULL COMMENT '开始时间',
  `end_time` datetime DEFAULT NULL COMMENT '结束时间',
  `sort_order` int DEFAULT '0' COMMENT '显示顺序',
  `status` char(1) COLLATE utf8mb4_general_ci DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `create_by` varchar(64) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`activity_id`),
  KEY `idx_sort_order` (`sort_order`),
  KEY `idx_status` (`status`),
  KEY `idx_start_time` (`start_time`),
  KEY `idx_end_time` (`end_time`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='APP活动管理表';

-- ----------------------------
-- Records of app_activity (示例数据)
-- ----------------------------
BEGIN;
INSERT INTO `app_activity` (`activity_id`, `activity_name`, `activity_image`, `activity_url`, `activity_desc`, `start_time`, `end_time`, `sort_order`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES 
(1, '新用户注册送好礼', '', 'https://example.com/register', '新用户注册即可获得丰厚奖励', '2025-01-01 00:00:00', '2025-12-31 23:59:59', 1, '0', 'admin', NOW(), '', NULL, '新用户活动'),
(2, '春节特惠活动', '', 'https://example.com/spring', '春节期间特价优惠，不容错过', '2025-02-01 00:00:00', '2025-02-28 23:59:59', 2, '0', 'admin', NOW(), '', NULL, '春节活动');
COMMIT;

-- ----------------------------
-- 管理后台菜单配置
-- ----------------------------

-- 活动管理菜单
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES
('活动管理', 2000, 5, 'activity', 'fuguang/activity/index', '', 1, 0, 'C', '0', '0', 'fuguang:activity:list', 'star', 'admin', NOW(), '', NULL, '活动管理菜单');

-- 获取刚插入的活动管理菜单ID
SET @activityMenuId = LAST_INSERT_ID();

-- 活动管理子菜单
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES
('活动查询', @activityMenuId, 1, '#', '', '', 1, 0, 'F', '0', '0', 'fuguang:activity:query', '#', 'admin', NOW(), '', NULL, ''),
('活动新增', @activityMenuId, 2, '#', '', '', 1, 0, 'F', '0', '0', 'fuguang:activity:add', '#', 'admin', NOW(), '', NULL, ''),
('活动修改', @activityMenuId, 3, '#', '', '', 1, 0, 'F', '0', '0', 'fuguang:activity:edit', '#', 'admin', NOW(), '', NULL, ''),
('活动删除', @activityMenuId, 4, '#', '', '', 1, 0, 'F', '0', '0', 'fuguang:activity:remove', '#', 'admin', NOW(), '', NULL, ''),
('活动导出', @activityMenuId, 5, '#', '', '', 1, 0, 'F', '0', '0', 'fuguang:activity:export', '#', 'admin', NOW(), '', NULL, '');
