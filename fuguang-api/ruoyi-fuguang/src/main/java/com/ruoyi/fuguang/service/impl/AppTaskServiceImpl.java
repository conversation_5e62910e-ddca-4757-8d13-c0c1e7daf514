package com.ruoyi.fuguang.service.impl;

import java.math.BigDecimal;
import java.util.List;

import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.service.ISysConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.fuguang.mapper.AppTaskMapper;
import com.ruoyi.fuguang.domain.AppTask;
import com.ruoyi.fuguang.service.IAppTaskService;
import com.ruoyi.fuguang.service.IAppUserService;
import com.ruoyi.fuguang.service.IAppTaskImageService;
import com.ruoyi.fuguang.service.IUserBalanceService;
import com.ruoyi.fuguang.service.ICommissionBillService;
import com.ruoyi.fuguang.service.IAppUserProfileService;
import com.ruoyi.fuguang.service.IAppUserTimelineService;
import com.ruoyi.fuguang.domain.AppUser;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * APP任务Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-01-18
 */
@Service
public class AppTaskServiceImpl implements IAppTaskService
{
    private static final Logger log = LoggerFactory.getLogger(AppTaskServiceImpl.class);

    @Autowired
    private AppTaskMapper appTaskMapper;

    @Autowired
    private IAppUserService appUserService;

    @Autowired
    private IAppTaskImageService appTaskImageService;

    @Autowired
    private IUserBalanceService userBalanceService;

    @Autowired
    private ICommissionBillService commissionBillService;

    @Autowired
    private IAppUserProfileService appUserProfileService;

    @Autowired
    private IAppUserTimelineService appUserTimelineService;

    @Autowired
    private ISysConfigService sysConfigService;

    /**
     * 查询APP任务
     *
     * @param taskId APP任务主键
     * @return APP任务
     */
    @Override
    public AppTask selectAppTaskByTaskId(Long taskId)
    {
        AppTask task = appTaskMapper.selectAppTaskByTaskId(taskId);
        if (task != null) {
            // 查询任务图片
            task.setTaskImages(appTaskImageService.selectAppTaskImagesByTaskId(taskId));
            task.setPublisherUserProfile(appUserProfileService.selectAppUserProfileByUserId(task.getPublisherId()));
            task.setReceiverUserProfile(appUserProfileService.selectAppUserProfileByUserId(task.getReceiverId()));
        }
        return task;
    }

    /**
     * 查询APP任务列表
     * 
     * @param appTask APP任务
     * @return APP任务
     */
    @Override
    public List<AppTask> selectAppTaskList(AppTask appTask)
    {
        return appTaskMapper.selectAppTaskList(appTask);
    }

    /**
     * 新增APP任务
     *
     * @param appTask APP任务
     * @return 结果
     */
    @Override
    @Transactional
    public AppTask insertAppTask(AppTask appTask)
    {
        // 设置发布者信息
        if (appTask.getPublisherId() != null) {
            AppUser publisher = appUserService.selectAppUserByUserId(appTask.getPublisherId());
            if (publisher != null) {
                appTask.setPublisherName(publisher.getNickName());
                appTask.setPublisherAvatar(publisher.getAvatar());
            }
        }
        appTask.setTaskStatus("0"); // 待支付
        appTask.setViewCount(0);
        appTask.setHotScore(0);
        appTask.setCreateTime(DateUtils.getNowDate());
        appTask.setMaintenanceFeeProportion(new BigDecimal(sysConfigService.selectConfigByKey("appTask.maintenance.fee.proportion")));
        appTask.setGuaranteeProportion(new  BigDecimal(sysConfigService.selectConfigByKey("appTask.guarantee.fee.proportion")));
        // 保存任务基本信息
        int result = appTaskMapper.insertAppTask(appTask);
        System.out.println("任务保存结果: " + result + ", 任务ID: " + appTask.getTaskId());
        // 保存任务图片
        if (result > 0 && appTask.getImages() != null && !appTask.getImages().isEmpty()) {
            System.out.println("开始保存任务图片，数量: " + appTask.getImages().size());
            int imageResult = appTaskImageService.insertAppTaskImageBatch(appTask.getTaskId(), appTask.getImages());
            System.out.println("图片保存结果: " + imageResult);
        } else {
            System.out.println("没有图片需要保存");
        }
        return appTask;
    }

    /**
     * 修改APP任务
     *
     * @param appTask APP任务
     * @return 结果
     */
    @Override
    @Transactional
    public int updateAppTask(AppTask appTask)
    {
        appTask.setUpdateTime(DateUtils.getNowDate());
        int result = appTaskMapper.updateAppTask(appTask);
        // 更新任务图片
        if (result > 0 && appTask.getImages() != null) {
            // 先删除原有图片
            appTaskImageService.deleteAppTaskImagesByTaskId(appTask.getTaskId());
            // 再添加新图片
            if (!appTask.getImages().isEmpty()) {
                appTaskImageService.insertAppTaskImageBatch(appTask.getTaskId(), appTask.getImages());
            }
        }
        return result;
    }

    /**
     * 批量删除APP任务
     * 
     * @param taskIds 需要删除的APP任务主键
     * @return 结果
     */
    @Override
    public int deleteAppTaskByTaskIds(Long[] taskIds)
    {
        return appTaskMapper.deleteAppTaskByTaskIds(taskIds);
    }

    /**
     * 删除APP任务信息
     * 
     * @param taskId APP任务主键
     * @return 结果
     */
    @Override
    public int deleteAppTaskByTaskId(Long taskId)
    {
        return appTaskMapper.deleteAppTaskByTaskId(taskId);
    }

    /**
     * 查询热门任务列表
     * 
     * @param longitude 经度
     * @param latitude 纬度
     * @param limit 限制数量
     * @return 任务列表
     */
    @Override
    public List<AppTask> selectHotTaskList(String longitude, String latitude, Integer limit)
    {
        return appTaskMapper.selectHotTaskList(longitude, latitude, limit);
    }

    /**
     * 增加任务浏览次数
     * 
     * @param taskId 任务ID
     * @return 结果
     */
    @Override
    public int increaseViewCount(Long taskId)
    {
        return appTaskMapper.increaseViewCount(taskId);
    }

    /**
     * 接取任务
     * 
     * @param taskId 任务ID
     * @param userId 用户ID
     * @return 结果
     */
    @Override
    @Transactional
    public int acceptTask(Long taskId, Long userId)
    {
        // 获取用户信息
        AppUser user = appUserService.selectAppUserByUserId(userId);
        String userName = user != null ? user.getNickName() : "";

        int result = appTaskMapper.acceptTask(taskId, userId, userName);

        if (result > 0) {
            // 更新用户个人简介统计（总任务数+1）
            appUserProfileService.updateTaskStats(
                userId,
                1, // 总任务数+1
                0, // 完成任务数不变
                java.math.BigDecimal.ZERO // 收益不变
            );
        }

        return result;
    }

    /**
     * 完成任务
     *
     * @param taskId 任务ID
     * @return 结果
     */
    @Override
    @Transactional
    public AjaxResult completeTask(Long taskId)
    {
        try {
            // 获取任务信息
            AppTask task = selectAppTaskByTaskId(taskId);
            if (StringUtils.isNull(task)) {
                log.error("任务不存在，任务ID：{}", taskId);
                return AjaxResult.error("任务不存在");
            }
            // 更新任务状态为已完成
            int result = appTaskMapper.completeTask(taskId);
            if (result > 0 && task.getReceiverId() != null) {
                // 获取接收者信息
                AppUser receiver = appUserService.selectAppUserByUserId(task.getReceiverId());
                if (receiver != null) {
                    // 初始化用户余额（如果不存在）
                    userBalanceService.initUserBalance(task.getReceiverId(), receiver.getNickName());
                    //保障金
                    BigDecimal guaranteeAmount=task.getTaskAmount().multiply(task.getGuaranteeProportion());
                    //任务获取总额
                    BigDecimal amount=task.getTaskAmount().subtract(task.getTaskAmount().multiply(task.getMaintenanceFeeProportion()));
                    // 增加用户余额（任务佣金）- 这会自动更新佣金账单
                    userBalanceService.increaseBalance(task.getReceiverId(),amount,"task_commission",task.getTaskId(),"TASK_" + task.getTaskId(),"完成任务获得佣金：" + task.getTaskTitle(),"1");
                    //冻结用户保障金
                    userBalanceService.freezeBalance(task.getReceiverId(), guaranteeAmount);
                    // 更新用户个人简介统计
                    appUserProfileService.updateTaskStats(
                            task.getReceiverId(),
                            0, // 总任务数不变（接取时已增加）
                            1, // 完成任务数+1
                            amount // 收益增加
                    );
                    // 更新任务分（完成任务获得10分）
                    appUserProfileService.updateTaskScore(task.getReceiverId(), 10);
                    // 添加任务完成事件到时间线
                    appUserTimelineService.addTaskCompleteEvent(
                            task.getReceiverId(),
                            task.getTaskId(),
                            task.getTaskTitle(),
                            amount,
                            DateUtils.getNowDate()
                    );
                    // 为发单人添加评价提醒事件到时间线
                    String evaluationEventTitle = "任务完成，可以评价";
                    String evaluationEventDesc = String.format("任务「%s」已完成，您可以对接单人「%s」进行评价",
                            task.getTaskTitle(), receiver.getNickName());
                    String evaluationEventData = String.format("{\"taskId\":%d,\"receiverId\":%d,\"receiverName\":\"%s\",\"canEvaluate\":true}",
                            task.getTaskId(), task.getReceiverId(), receiver.getNickName());
                    appUserTimelineService.addCustomEvent(
                            task.getPublisherId(),
                            "task_evaluation_reminder",
                            evaluationEventTitle,
                            evaluationEventDesc,
                            evaluationEventData,
                            DateUtils.getNowDate(),
                            "star-o",
                            "primary"
                    );
                    log.info("任务完成佣金分配成功，任务ID：{}，接收者ID：{}，佣金金额：{}",
                            taskId, task.getReceiverId(), task.getTaskAmount());
                } else {
                    log.error("任务接收者不存在，任务ID：{}，接收者ID：{}", taskId, task.getReceiverId());
                }
            }
            return AjaxResult.success();
        } catch (Exception e) {
            log.error("完成任务异常，任务ID：{}", taskId, e);
            throw e;
        }
    }

    /**
     * 取消任务
     * 
     * @param taskId 任务ID
     * @return 结果
     */
    @Override
    public int cancelTask(Long taskId)
    {
        return appTaskMapper.cancelTask(taskId);
    }

    /**
     * 查询用户发布的任务
     * 
     * @param userId 用户ID
     * @return 任务列表
     */
    @Override
    public List<AppTask> selectTasksByPublisher(Long userId)
    {
        return appTaskMapper.selectTasksByPublisher(userId);
    }

    /**
     * 查询用户接取的任务
     *
     * @param userId 用户ID
     * @return 任务列表
     */
    @Override
    public List<AppTask> selectTasksByReceiver(Long userId)
    {
        return appTaskMapper.selectTasksByReceiver(userId);
    }

    /**
     * 更新任务状态为待接取
     *
     * @param taskId 任务ID
     * @return 结果
     */
    @Override
    public int updateTaskStatusToWaitingAccept(Long taskId)
    {
        return appTaskMapper.updateTaskStatusToWaitingAccept(taskId);
    }

    /**
     * 终止任务
     *
     * @param taskId 任务ID
     * @return 结果
     */
    @Override
    public int terminateTask(Long taskId)
    {
        return appTaskMapper.terminateTask(taskId);
    }
}
