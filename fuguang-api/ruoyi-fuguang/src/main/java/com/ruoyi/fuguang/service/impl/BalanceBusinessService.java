package com.ruoyi.fuguang.service.impl;

import java.math.BigDecimal;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.ruoyi.fuguang.service.IUserBalanceService;
import com.ruoyi.fuguang.service.ICommissionBillService;
import com.ruoyi.fuguang.service.IBalanceRecordService;
import com.ruoyi.fuguang.domain.UserBalance;
import com.ruoyi.fuguang.domain.BalanceRecord;
import com.ruoyi.fuguang.domain.CommissionBill;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 余额业务服务类
 * 用于处理复杂的余额业务逻辑，确保三个模块的数据一致性
 * 
 * <AUTHOR>
 * @date 2025-01-23
 */
@Service
public class BalanceBusinessService 
{
    private static final Logger log = LoggerFactory.getLogger(BalanceBusinessService.class);

    @Autowired
    private IUserBalanceService userBalanceService;
    
    @Autowired
    private ICommissionBillService commissionBillService;
    
    @Autowired
    private IBalanceRecordService balanceRecordService;

    /**
     * 验证用户余额数据一致性
     * 检查用户余额、余额记录、佣金账单之间的数据是否一致
     * 
     * @param userId 用户ID
     * @return 验证结果
     */
    @Transactional(readOnly = true)
    public boolean validateBalanceConsistency(Long userId) {
        try {
            // 获取用户余额
            UserBalance userBalance = userBalanceService.selectUserBalanceByUserId(userId);
            if (userBalance == null) {
                log.warn("用户余额不存在，用户ID：{}", userId);
                return false;
            }

            // 获取用户所有余额变动记录
            java.util.List<BalanceRecord> records = balanceRecordService.selectBalanceRecordListByUserId(userId);
            
            // 计算收入总额
            BigDecimal totalIncome = BigDecimal.ZERO;
            BigDecimal totalWithdraw = BigDecimal.ZERO;
            BigDecimal taskCommissionTotal = BigDecimal.ZERO;
            BigDecimal recommendRewardTotal = BigDecimal.ZERO;
            BigDecimal otherIncomeTotal = BigDecimal.ZERO;
            
            for (BalanceRecord record : records) {
                if ("1".equals(record.getChangeType())) {
                    // 收入
                    totalIncome = totalIncome.add(record.getChangeAmount());
                    
                    // 按收入类型分类统计
                    if ("1".equals(record.getIncomeType())) {
                        taskCommissionTotal = taskCommissionTotal.add(record.getChangeAmount());
                    } else if ("2".equals(record.getIncomeType())) {
                        recommendRewardTotal = recommendRewardTotal.add(record.getChangeAmount());
                    } else if ("3".equals(record.getIncomeType())) {
                        otherIncomeTotal = otherIncomeTotal.add(record.getChangeAmount());
                    }
                } else if ("2".equals(record.getChangeType())) {
                    // 支出
                    if ("withdraw".equals(record.getBusinessType())) {
                        totalWithdraw = totalWithdraw.add(record.getChangeAmount());
                    }
                }
            }

            // 验证用户余额表的累计收入和累计提现
            boolean incomeMatch = userBalance.getTotalIncome().compareTo(totalIncome) == 0;
            boolean withdrawMatch = userBalance.getTotalWithdraw().compareTo(totalWithdraw) == 0;
            
            if (!incomeMatch) {
                log.error("用户{}累计收入不一致，余额表：{}，记录计算：{}", 
                    userId, userBalance.getTotalIncome(), totalIncome);
            }
            
            if (!withdrawMatch) {
                log.error("用户{}累计提现不一致，余额表：{}，记录计算：{}", 
                    userId, userBalance.getTotalWithdraw(), totalWithdraw);
            }

            // 验证佣金账单数据（这里简化处理，实际应该按月验证）
            // 可以添加更详细的佣金账单验证逻辑
            
            return incomeMatch && withdrawMatch;
            
        } catch (Exception e) {
            log.error("验证用户余额一致性异常，用户ID：{}", userId, e);
            return false;
        }
    }

    /**
     * 修复用户余额数据不一致问题
     * 根据余额变动记录重新计算用户余额
     * 
     * @param userId 用户ID
     * @return 修复结果
     */
    @Transactional
    public boolean fixBalanceInconsistency(Long userId) {
        try {
            // 获取用户余额
            UserBalance userBalance = userBalanceService.selectUserBalanceByUserId(userId);
            if (userBalance == null) {
                log.warn("用户余额不存在，无法修复，用户ID：{}", userId);
                return false;
            }

            // 获取用户所有余额变动记录，按时间排序
            java.util.List<BalanceRecord> records = balanceRecordService.selectBalanceRecordListByUserId(userId);
            
            // 重新计算余额
            BigDecimal totalIncome = BigDecimal.ZERO;
            BigDecimal totalWithdraw = BigDecimal.ZERO;
            BigDecimal currentBalance = BigDecimal.ZERO;
            
            for (BalanceRecord record : records) {
                if ("1".equals(record.getChangeType())) {
                    // 收入
                    totalIncome = totalIncome.add(record.getChangeAmount());
                    currentBalance = currentBalance.add(record.getChangeAmount());
                } else if ("2".equals(record.getChangeType())) {
                    // 支出
                    currentBalance = currentBalance.subtract(record.getChangeAmount());
                    if ("withdraw".equals(record.getBusinessType())) {
                        totalWithdraw = totalWithdraw.add(record.getChangeAmount());
                    }
                }
            }

            // 更新用户余额
            userBalance.setTotalIncome(totalIncome);
            userBalance.setTotalWithdraw(totalWithdraw);
            userBalance.setAvailableBalance(currentBalance);
            userBalance.setTotalBalance(currentBalance.add(userBalance.getFrozenBalance()));
            
            int result = userBalanceService.updateUserBalance(userBalance);
            
            if (result > 0) {
                log.info("用户余额修复成功，用户ID：{}，累计收入：{}，累计提现：{}，当前余额：{}", 
                    userId, totalIncome, totalWithdraw, currentBalance);
                return true;
            } else {
                log.error("用户余额修复失败，用户ID：{}", userId);
                return false;
            }
            
        } catch (Exception e) {
            log.error("修复用户余额异常，用户ID：{}", userId, e);
            return false;
        }
    }

    /**
     * 获取业务类型中文名称
     * 
     * @param businessType 业务类型
     * @return 中文名称
     */
    public String getBusinessTypeName(String businessType) {
        switch (businessType) {
            case "task_commission":
                return "任务佣金";
            case "withdraw":
                return "提现";
            case "withdraw_reject":
                return "提现拒绝";
            case "withdraw_fail":
                return "提现失败";
            case "withdraw_rollback":
                return "提现回滚";
            case "admin_adjust":
                return "管理员调整";
            case "refund":
                return "退款";
            case "recommend_reward":
                return "推荐奖励";
            case "other_income":
                return "其他收入";
            default:
                return businessType;
        }
    }

    /**
     * 获取收入类型中文名称
     * 
     * @param incomeType 收入类型
     * @return 中文名称
     */
    public String getIncomeTypeName(String incomeType) {
        if (incomeType == null) {
            return "无";
        }
        switch (incomeType) {
            case "1":
                return "任务佣金";
            case "2":
                return "推荐奖励";
            case "3":
                return "其他收入";
            default:
                return incomeType;
        }
    }
}
