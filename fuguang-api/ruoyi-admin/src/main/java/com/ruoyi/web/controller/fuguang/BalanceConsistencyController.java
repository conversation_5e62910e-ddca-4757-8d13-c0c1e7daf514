package com.ruoyi.web.controller.fuguang;

import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.HashMap;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.fuguang.service.IUserBalanceService;
import com.ruoyi.fuguang.service.impl.BalanceBusinessService;
import com.ruoyi.fuguang.domain.UserBalance;

/**
 * 余额数据一致性管理Controller
 * 
 * <AUTHOR>
 * @date 2025-01-23
 */
@RestController
@RequestMapping("/fuguang/balance/consistency")
public class BalanceConsistencyController extends BaseController
{
    @Autowired
    private BalanceBusinessService balanceBusinessService;
    
    @Autowired
    private IUserBalanceService userBalanceService;

    /**
     * 验证单个用户余额数据一致性
     */
    @PreAuthorize("@ss.hasPermi('fuguang:balance:consistency')")
    @GetMapping("/validate/{userId}")
    public AjaxResult validateUserBalance(@PathVariable("userId") Long userId)
    {
        boolean isConsistent = balanceBusinessService.validateBalanceConsistency(userId);
        
        Map<String, Object> result = new HashMap<>();
        result.put("userId", userId);
        result.put("isConsistent", isConsistent);
        result.put("message", isConsistent ? "数据一致" : "数据不一致");
        
        return success(result);
    }

    /**
     * 验证所有用户余额数据一致性
     */
    @PreAuthorize("@ss.hasPermi('fuguang:balance:consistency')")
    @GetMapping("/validate/all")
    public AjaxResult validateAllUserBalance()
    {
        // 获取所有用户余额
        List<UserBalance> allBalances = userBalanceService.selectUserBalanceList(new UserBalance());
        
        List<Map<String, Object>> results = new ArrayList<>();
        int consistentCount = 0;
        int inconsistentCount = 0;
        
        for (UserBalance balance : allBalances) {
            boolean isConsistent = balanceBusinessService.validateBalanceConsistency(balance.getUserId());
            
            Map<String, Object> result = new HashMap<>();
            result.put("userId", balance.getUserId());
            result.put("userName", balance.getUserName());
            result.put("isConsistent", isConsistent);
            result.put("totalBalance", balance.getTotalBalance());
            result.put("availableBalance", balance.getAvailableBalance());
            result.put("totalIncome", balance.getTotalIncome());
            result.put("totalWithdraw", balance.getTotalWithdraw());
            
            results.add(result);
            
            if (isConsistent) {
                consistentCount++;
            } else {
                inconsistentCount++;
            }
        }
        
        Map<String, Object> summary = new HashMap<>();
        summary.put("totalUsers", allBalances.size());
        summary.put("consistentCount", consistentCount);
        summary.put("inconsistentCount", inconsistentCount);
        summary.put("details", results);
        
        return success(summary);
    }

    /**
     * 修复单个用户余额数据不一致问题
     */
    @PreAuthorize("@ss.hasPermi('fuguang:balance:fix')")
    @Log(title = "修复用户余额数据", businessType = BusinessType.UPDATE)
    @PostMapping("/fix/{userId}")
    public AjaxResult fixUserBalance(@PathVariable("userId") Long userId)
    {
        // 先验证是否存在不一致
        boolean isConsistent = balanceBusinessService.validateBalanceConsistency(userId);
        if (isConsistent) {
            return success("用户余额数据一致，无需修复");
        }
        
        // 执行修复
        boolean fixResult = balanceBusinessService.fixBalanceInconsistency(userId);
        
        if (fixResult) {
            // 再次验证修复结果
            boolean afterFix = balanceBusinessService.validateBalanceConsistency(userId);
            if (afterFix) {
                return success("用户余额数据修复成功");
            } else {
                return error("用户余额数据修复后仍不一致，请检查数据");
            }
        } else {
            return error("用户余额数据修复失败");
        }
    }

    /**
     * 批量修复所有用户余额数据不一致问题
     */
    @PreAuthorize("@ss.hasPermi('fuguang:balance:fix')")
    @Log(title = "批量修复用户余额数据", businessType = BusinessType.UPDATE)
    @PostMapping("/fix/all")
    public AjaxResult fixAllUserBalance()
    {
        // 获取所有用户余额
        List<UserBalance> allBalances = userBalanceService.selectUserBalanceList(new UserBalance());
        
        int totalUsers = allBalances.size();
        int fixedCount = 0;
        int failedCount = 0;
        List<Map<String, Object>> failedUsers = new ArrayList<>();
        
        for (UserBalance balance : allBalances) {
            // 先验证是否存在不一致
            boolean isConsistent = balanceBusinessService.validateBalanceConsistency(balance.getUserId());
            if (!isConsistent) {
                // 执行修复
                boolean fixResult = balanceBusinessService.fixBalanceInconsistency(balance.getUserId());
                if (fixResult) {
                    fixedCount++;
                } else {
                    failedCount++;
                    Map<String, Object> failedUser = new HashMap<>();
                    failedUser.put("userId", balance.getUserId());
                    failedUser.put("userName", balance.getUserName());
                    failedUsers.add(failedUser);
                }
            }
        }
        
        Map<String, Object> result = new HashMap<>();
        result.put("totalUsers", totalUsers);
        result.put("fixedCount", fixedCount);
        result.put("failedCount", failedCount);
        result.put("failedUsers", failedUsers);
        result.put("message", String.format("批量修复完成，共处理%d个用户，成功修复%d个，失败%d个", 
                                           totalUsers, fixedCount, failedCount));
        
        return success(result);
    }

    /**
     * 获取业务类型说明
     */
    @PreAuthorize("@ss.hasPermi('fuguang:balance:query')")
    @GetMapping("/business-types")
    public AjaxResult getBusinessTypes()
    {
        Map<String, String> businessTypes = new HashMap<>();
        businessTypes.put("task_commission", balanceBusinessService.getBusinessTypeName("task_commission"));
        businessTypes.put("withdraw", balanceBusinessService.getBusinessTypeName("withdraw"));
        businessTypes.put("withdraw_reject", balanceBusinessService.getBusinessTypeName("withdraw_reject"));
        businessTypes.put("withdraw_fail", balanceBusinessService.getBusinessTypeName("withdraw_fail"));
        businessTypes.put("withdraw_rollback", balanceBusinessService.getBusinessTypeName("withdraw_rollback"));
        businessTypes.put("admin_adjust", balanceBusinessService.getBusinessTypeName("admin_adjust"));
        businessTypes.put("refund", balanceBusinessService.getBusinessTypeName("refund"));
        businessTypes.put("recommend_reward", balanceBusinessService.getBusinessTypeName("recommend_reward"));
        businessTypes.put("other_income", balanceBusinessService.getBusinessTypeName("other_income"));
        
        return success(businessTypes);
    }

    /**
     * 获取收入类型说明
     */
    @PreAuthorize("@ss.hasPermi('fuguang:balance:query')")
    @GetMapping("/income-types")
    public AjaxResult getIncomeTypes()
    {
        Map<String, String> incomeTypes = new HashMap<>();
        incomeTypes.put("1", balanceBusinessService.getIncomeTypeName("1"));
        incomeTypes.put("2", balanceBusinessService.getIncomeTypeName("2"));
        incomeTypes.put("3", balanceBusinessService.getIncomeTypeName("3"));
        
        return success(incomeTypes);
    }
}
