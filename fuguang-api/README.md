<p align="center">
	<img alt="logo" src="https://oscimg.oschina.net/oscnet/up-d3d0a9303e11d522a06cd263f3079027715.png">
</p>
<h1 align="center" style="margin: 30px 0 30px; font-weight: bold;">RuoYi v3.9.0</h1>
<h4 align="center">基于SpringBoot+Vue前后端分离的Java快速开发框架</h4>
<p align="center">
	<a href="https://gitee.com/y_project/RuoYi-Vue/stargazers"><img src="https://gitee.com/y_project/RuoYi-Vue/badge/star.svg?theme=dark"></a>
	<a href="https://gitee.com/y_project/RuoYi-Vue"><img src="https://img.shields.io/badge/RuoYi-v3.9.0-brightgreen.svg"></a>
	<a href="https://gitee.com/y_project/RuoYi-Vue/blob/master/LICENSE"><img src="https://img.shields.io/github/license/mashape/apistatus.svg"></a>
</p>

## 平台简介

浮光壁垒是基于若依框架开发的任务发布平台，提供完整的任务管理和用户服务功能。

- 前端采用 Vue、Element UI。
- 后端采用 Spring Boot、Spring Security、Redis & Jwt。
- 权限认证使用 Jwt，支持多终端认证系统。
- 支持加载动态权限菜单，多方式轻松权限控制。
- 高效率开发，使用代码生成器可以一键生成前后端代码。

## 最新更新 - 余额系统重构 (2025-01-23)

### 修改内容

本次更新完善了用户余额记录、佣金账单、用户余额三个模块的业务关系，确保数据一致性：

#### 1. 核心业务流程优化

**任务完成流程**：

- 任务完成 → 增加用户余额 → 自动记录余额变动 → 自动更新佣金账单
- 移除了重复的佣金账单更新调用，统一通过 `UserBalanceService.increaseBalance()` 处理

**提现流程**：

- 申请提现 → 立即扣除用户余额 → 记录余额变动 → 更新佣金账单
- 提现成功 → 无需额外操作（余额已扣除）
- 提现失败/拒绝 → 返还用户余额 → 记录余额变动

#### 2. 修改的文件

**后端核心文件**：

- `UserBalanceServiceImpl.java` - 增加佣金账单自动更新逻辑
- `WithdrawServiceImpl.java` - 完善提现申请、成功、失败的余额处理
- `AppTaskServiceImpl.java` - 简化任务完成的佣金分配逻辑

**新增功能文件**：

- `BalanceBusinessService.java` - 余额业务服务，提供数据一致性验证和修复
- `BalanceConsistencyController.java` - 余额数据一致性管理控制器
- `balanceConsistency.js` - 前端 API 接口
- `balanceConsistency/index.vue` - 余额数据一致性管理页面

#### 3. 业务逻辑改进

**收入处理**：

- 任务佣金（incomeType=1）→ 自动更新佣金账单的任务佣金字段
- 推荐奖励（incomeType=2）→ 自动更新佣金账单的推荐奖励字段
- 其他收入（incomeType=3）→ 自动更新佣金账单的其他收入字段

**支出处理**：

- 提现申请 → 立即扣除余额，记录变动，更新佣金账单提现金额
- 提现失败/拒绝 → 返还余额，记录变动（不影响佣金账单）

**数据一致性**：

- 提供数据一致性验证功能
- 提供数据修复功能
- 支持单用户和批量操作

#### 4. 新增管理功能

访问路径：`系统管理 → 余额数据一致性`

功能包括：

- 验证单个用户余额数据一致性
- 验证所有用户余额数据一致性
- 修复单个用户余额数据
- 批量修复所有用户余额数据
- 查看统计信息和一致性比例

#### 5. 业务类型说明

| 业务类型         | 说明     | 影响模块       |
| ---------------- | -------- | -------------- |
| task_commission  | 任务佣金 | 余额+记录+账单 |
| withdraw         | 提现申请 | 余额+记录+账单 |
| withdraw_reject  | 提现拒绝 | 余额+记录      |
| withdraw_fail    | 提现失败 | 余额+记录      |
| recommend_reward | 推荐奖励 | 余额+记录+账单 |
| other_income     | 其他收入 | 余额+记录+账单 |

### 使用说明

1. **正常业务操作**：所有余额相关操作都会自动维护三个模块的数据一致性
2. **数据验证**：定期使用余额数据一致性管理功能验证数据完整性
3. **问题修复**：发现数据不一致时，可使用修复功能自动修正

### 注意事项

- 所有余额操作都在事务中执行，确保数据一致性
- 提现申请时立即扣除余额，避免重复提现
- 提现失败或拒绝时会自动返还余额
- 建议定期运行数据一致性检查

## 新增功能：用户个人简介系统

### 功能概述

为 APP 用户添加了完整的个人简介功能，包含信用分、任务分、扶贫救援标志、个人描述、图片视频展示以及履历时间线等功能。

### 主要特性

#### 1. 个人简介管理

- **信用分系统**：默认 100 分，根据用户行为动态调整
- **任务分系统**：完成任务获得积分，体现用户活跃度
- **扶贫救援徽章**：参与公益活动可获得特殊标识
- **个人描述**：用户可自定义个人介绍文字
- **多媒体展示**：支持上传多张图片和短视频
- **等级系统**：基于经验值的用户等级划分

#### 2. 履历时间线

- **自动记录**：系统自动记录用户重要事件
- **事件类型**：用户注册、任务完成、获得奖励、等级提升等
- **时间线展示**：按时间倒序展示用户成长历程

#### 3. 统计数据

- 总任务数 / 完成任务数 / 任务成功率
- 总收益统计 / 用户等级和经验值

### 技术实现

#### 数据库表结构

1. **app_user_profile** - 用户个人简介表
2. **app_user_timeline** - 用户履历时间线表

#### 后端接口

1. **管理后台接口**：`/fuguang/profile/*` `/fuguang/timeline/*`
2. **APP 端接口**：`/app/profile/*`

#### 自动化功能

- 用户注册时自动创建个人简介和记录时间线
- 任务完成时自动更新统计数据和记录事件
- 经验值和等级自动计算

## 内置功能

1.  用户管理：用户是系统操作者，该功能主要完成系统用户配置。
2.  部门管理：配置系统组织机构（公司、部门、小组），树结构展现支持数据权限。
3.  岗位管理：配置系统用户所属担任职务。
4.  菜单管理：配置系统菜单，操作权限，按钮权限标识等。
5.  角色管理：角色菜单权限分配、设置角色按机构进行数据范围权限划分。
6.  字典管理：对系统中经常使用的一些较为固定的数据进行维护。
7.  参数管理：对系统动态配置常用参数。
8.  通知公告：系统通知公告信息发布维护。
9.  操作日志：系统正常操作日志记录和查询；系统异常信息日志记录和查询。
10. 登录日志：系统登录日志记录查询包含登录异常。
11. 在线用户：当前系统中活跃用户状态监控。
12. 定时任务：在线（添加、修改、删除)任务调度包含执行结果日志。
13. 代码生成：前后端代码的生成（java、html、xml、sql）支持 CRUD 下载 。
14. 系统接口：根据业务代码自动生成相关的 api 接口文档。
15. 服务监控：监视当前系统 CPU、内存、磁盘、堆栈等相关信息。
16. 缓存监控：对系统的缓存信息查询，命令统计等。
17. 在线构建器：拖动表单元素生成相应的 HTML 代码。
18. 连接池监视：监视当前系统数据库连接池状态，可进行分析 SQL 找出系统性能瓶颈。

## 在线体验

- admin/admin123
- 陆陆续续收到一些打赏，为了更好的体验已用于演示服务器升级。谢谢各位小伙伴。

演示地址：http://vue.ruoyi.vip  
文档地址：http://doc.ruoyi.vip

## 演示图

<table>
    <tr>
        <td><img src="https://oscimg.oschina.net/oscnet/cd1f90be5f2684f4560c9519c0f2a232ee8.jpg"/></td>
        <td><img src="https://oscimg.oschina.net/oscnet/1cbcf0e6f257c7d3a063c0e3f2ff989e4b3.jpg"/></td>
    </tr>
    <tr>
        <td><img src="https://oscimg.oschina.net/oscnet/up-8074972883b5ba0622e13246738ebba237a.png"/></td>
        <td><img src="https://oscimg.oschina.net/oscnet/up-9f88719cdfca9af2e58b352a20e23d43b12.png"/></td>
    </tr>
    <tr>
        <td><img src="https://oscimg.oschina.net/oscnet/up-39bf2584ec3a529b0d5a3b70d15c9b37646.png"/></td>
        <td><img src="https://oscimg.oschina.net/oscnet/up-936ec82d1f4872e1bc980927654b6007307.png"/></td>
    </tr>
	<tr>
        <td><img src="https://oscimg.oschina.net/oscnet/up-b2d62ceb95d2dd9b3fbe157bb70d26001e9.png"/></td>
        <td><img src="https://oscimg.oschina.net/oscnet/up-d67451d308b7a79ad6819723396f7c3d77a.png"/></td>
    </tr>	 
    <tr>
        <td><img src="https://oscimg.oschina.net/oscnet/5e8c387724954459291aafd5eb52b456f53.jpg"/></td>
        <td><img src="https://oscimg.oschina.net/oscnet/644e78da53c2e92a95dfda4f76e6d117c4b.jpg"/></td>
    </tr>
	<tr>
        <td><img src="https://oscimg.oschina.net/oscnet/up-8370a0d02977eebf6dbf854c8450293c937.png"/></td>
        <td><img src="https://oscimg.oschina.net/oscnet/up-49003ed83f60f633e7153609a53a2b644f7.png"/></td>
    </tr>
	<tr>
        <td><img src="https://oscimg.oschina.net/oscnet/up-d4fe726319ece268d4746602c39cffc0621.png"/></td>
        <td><img src="https://oscimg.oschina.net/oscnet/up-c195234bbcd30be6927f037a6755e6ab69c.png"/></td>
    </tr>
    <tr>
        <td><img src="https://oscimg.oschina.net/oscnet/b6115bc8c31de52951982e509930b20684a.jpg"/></td>
        <td><img src="https://oscimg.oschina.net/oscnet/up-5e4daac0bb59612c5038448acbcef235e3a.png"/></td>
    </tr>
</table>

## 商城管理功能完善

### 已完成功能

1. **删除购物车功能**

   - 删除了所有购物车相关的代码文件
   - 删除了购物车相关的数据库表
   - 提供了删除脚本：`fuguang-api/sql/remove_cart.sql`

2. **商品规格管理**

   - 创建了商品规格管理前端页面：`fuguang-web/src/views/mall/spec/index.vue`
   - 创建了商品规格 API 接口：`fuguang-web/src/api/mall/spec.js`
   - 完善了后端 Controller 的上下架功能
   - 支持规格的增删改查、上下架等操作

3. **物流管理**

   - 创建了物流管理前端页面：`fuguang-web/src/views/mall/logistics/index.vue`
   - 创建了物流管理 API 接口：`fuguang-web/src/api/mall/logistics.js`
   - 支持物流信息的管理、发货记录创建、状态更新等功能

4. **商品管理功能完善**

   - 添加了批量上架、批量下架功能
   - 优化了商品状态显示（使用开关组件）
   - 完善了商品状态切换的交互体验

5. **订单管理功能完善**

   - 添加了退款功能按钮
   - 添加了批量发货、批量取消、批量退款功能
   - 优化了订单操作按钮的显示逻辑

6. **数据字典配置**
   - 创建了商城管理相关的数据字典配置：`fuguang-api/sql/mall_dict.sql`
   - 包含商品状态、订单状态、支付状态、规格状态、物流状态等字典

### 部署说明

1. **执行数据库脚本**

   ```sql
   -- 删除购物车功能（可选）
   source fuguang-api/sql/remove_cart.sql;

   -- 添加数据字典配置
   source fuguang-api/sql/mall_dict.sql;
   ```

2. **重启应用服务**

   - 确保新的功能模块被正确加载

3. **验证功能**
   - 登录管理后台
   - 检查"商城管理"菜单下的各个功能模块
   - 测试商品规格管理、物流管理等新功能

### 功能特点

- **完整的商城管理体系**：涵盖商品、订单、物流等核心功能
- **批量操作支持**：提高管理效率
- **状态管理优化**：直观的状态显示和切换
- **用户体验提升**：优化了交互逻辑和界面布局

## 若依前后端分离交流群

QQ 群： [![加入QQ群](https://img.shields.io/badge/已满-937441-blue.svg)](https://jq.qq.com/?_wv=1027&k=5bVB1og) [![加入QQ群](https://img.shields.io/badge/已满-887144332-blue.svg)](https://jq.qq.com/?_wv=1027&k=5eiA4DH) [![加入QQ群](https://img.shields.io/badge/已满-180251782-blue.svg)](https://jq.qq.com/?_wv=1027&k=5AxMKlC) [![加入QQ群](https://img.shields.io/badge/已满-104180207-blue.svg)](https://jq.qq.com/?_wv=1027&k=51G72yr) [![加入QQ群](https://img.shields.io/badge/已满-186866453-blue.svg)](https://jq.qq.com/?_wv=1027&k=VvjN2nvu) [![加入QQ群](https://img.shields.io/badge/已满-201396349-blue.svg)](https://jq.qq.com/?_wv=1027&k=5vYAqA05) [![加入QQ群](https://img.shields.io/badge/已满-101456076-blue.svg)](https://jq.qq.com/?_wv=1027&k=kOIINEb5) [![加入QQ群](https://img.shields.io/badge/已满-101539465-blue.svg)](https://jq.qq.com/?_wv=1027&k=UKtX5jhs) [![加入QQ群](https://img.shields.io/badge/已满-264312783-blue.svg)](https://jq.qq.com/?_wv=1027&k=EI9an8lJ) [![加入QQ群](https://img.shields.io/badge/已满-167385320-blue.svg)](https://jq.qq.com/?_wv=1027&k=SWCtLnMz) [![加入QQ群](https://img.shields.io/badge/已满-104748341-blue.svg)](https://jq.qq.com/?_wv=1027&k=96Dkdq0k) [![加入QQ群](https://img.shields.io/badge/已满-160110482-blue.svg)](https://jq.qq.com/?_wv=1027&k=0fsNiYZt) [![加入QQ群](https://img.shields.io/badge/已满-170801498-blue.svg)](https://jq.qq.com/?_wv=1027&k=7xw4xUG1) [![加入QQ群](https://img.shields.io/badge/已满-108482800-blue.svg)](https://jq.qq.com/?_wv=1027&k=eCx8eyoJ) [![加入QQ群](https://img.shields.io/badge/已满-101046199-blue.svg)](https://jq.qq.com/?_wv=1027&k=SpyH2875) [![加入QQ群](https://img.shields.io/badge/已满-136919097-blue.svg)](https://jq.qq.com/?_wv=1027&k=tKEt51dz) [![加入QQ群](https://img.shields.io/badge/已满-143961921-blue.svg)](http://qm.qq.com/cgi-bin/qm/qr?_wv=1027&k=0vBbSb0ztbBgVtn3kJS-Q4HUNYwip89G&authKey=8irq5PhutrZmWIvsUsklBxhj57l%2F1nOZqjzigkXZVoZE451GG4JHPOqW7AW6cf0T&noverify=0&group_code=143961921) [![加入QQ群](https://img.shields.io/badge/已满-174951577-blue.svg)](http://qm.qq.com/cgi-bin/qm/qr?_wv=1027&k=ZFAPAbp09S2ltvwrJzp7wGlbopsc0rwi&authKey=HB2cxpxP2yspk%2Bo3WKTBfktRCccVkU26cgi5B16u0KcAYrVu7sBaE7XSEqmMdFQp&noverify=0&group_code=174951577) [![加入QQ群](https://img.shields.io/badge/已满-161281055-blue.svg)](http://qm.qq.com/cgi-bin/qm/qr?_wv=1027&k=Fn2aF5IHpwsy8j6VlalNJK6qbwFLFHat&authKey=uyIT%2B97x2AXj3odyXpsSpVaPMC%2Bidw0LxG5MAtEqlrcBcWJUA%2FeS43rsF1Tg7IRJ&noverify=0&group_code=161281055) [![加入QQ群](https://img.shields.io/badge/已满-138988063-blue.svg)](http://qm.qq.com/cgi-bin/qm/qr?_wv=1027&k=XIzkm_mV2xTsUtFxo63bmicYoDBA6Ifm&authKey=dDW%2F4qsmw3x9govoZY9w%2FoWAoC4wbHqGal%2BbqLzoS6VBarU8EBptIgPKN%2FviyC8j&noverify=0&group_code=138988063) [![加入QQ群](https://img.shields.io/badge/已满-151450850-blue.svg)](http://qm.qq.com/cgi-bin/qm/qr?_wv=1027&k=DkugnCg68PevlycJSKSwjhFqfIgrWWwR&authKey=pR1Pa5lPIeGF%2FFtIk6d%2FGB5qFi0EdvyErtpQXULzo03zbhopBHLWcuqdpwY241R%2F&noverify=0&group_code=151450850) [![加入QQ群](https://img.shields.io/badge/已满-224622315-blue.svg)](http://qm.qq.com/cgi-bin/qm/qr?_wv=1027&k=F58bgRa-Dp-rsQJThiJqIYv8t4-lWfXh&authKey=UmUs4CVG5OPA1whvsa4uSespOvyd8%2FAr9olEGaWAfdLmfKQk%2FVBp2YU3u2xXXt76&noverify=0&group_code=224622315) [![加入QQ群](https://img.shields.io/badge/已满-287842588-blue.svg)](http://qm.qq.com/cgi-bin/qm/qr?_wv=1027&k=Nxb2EQ5qozWa218Wbs7zgBnjLSNk_tVT&authKey=obBKXj6SBKgrFTJZx0AqQnIYbNOvBB2kmgwWvGhzxR67RoRr84%2Bus5OadzMcdJl5&noverify=0&group_code=287842588) [![加入QQ群](https://img.shields.io/badge/已满-187944233-blue.svg)](http://qm.qq.com/cgi-bin/qm/qr?_wv=1027&k=numtK1M_I4eVd2Gvg8qtbuL8JgX42qNh&authKey=giV9XWMaFZTY%2FqPlmWbkB9g3fi0Ev5CwEtT9Tgei0oUlFFCQLDp4ozWRiVIzubIm&noverify=0&group_code=187944233) [![加入QQ群](https://img.shields.io/badge/已满-228578329-blue.svg)](http://qm.qq.com/cgi-bin/qm/qr?_wv=1027&k=G6r5KGCaa3pqdbUSXNIgYloyb8e0_L0D&authKey=4w8tF1eGW7%2FedWn%2FHAypQksdrML%2BDHolQSx7094Agm7Luakj9EbfPnSTxSi2T1LQ&noverify=0&group_code=228578329) [![加入QQ群](https://img.shields.io/badge/191164766-blue.svg)](http://qm.qq.com/cgi-bin/qm/qr?_wv=1027&k=GsOo-OLz53J8y_9TPoO6XXSGNRTgbFxA&authKey=R7Uy%2Feq%2BZsoKNqHvRKhiXpypW7DAogoWapOawUGHokJSBIBIre2%2FoiAZeZBSLuBc&noverify=0&group_code=191164766) 点击按钮入群。
